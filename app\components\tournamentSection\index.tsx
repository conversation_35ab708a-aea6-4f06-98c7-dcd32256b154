"use client";
import React, { useEffect, useMemo, useState } from "react";
import { TournamentSectionProps, TournamentDetails } from "@/app/types/CommonComponent.types";
import GroupedGameCard from "../groupedGameCard";
import { usePathname } from "next/navigation";
import HybridTournamentCard from "../hybridTournamentCard";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";

const TournamentSection: React.FC<TournamentSectionProps> = ({
  tournaments,
  sectionTitle,
  selectedDateFilter = null,
}) => {
  const pathname = usePathname();
  const isGameDetailsPage = pathname === "/game-details";
  const [tournamentDetails, setTournamentDetails] = useState<TournamentDetails[]>([]);
  const [tournamentBookingsInfo, setTournamentBookingsInfo] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);



  // Filter tournaments based on selected date
  const filteredTournaments = useMemo(() => {
    if (!isGameDetailsPage || !selectedDateFilter) return tournaments;

    return tournaments?.filter((_, index) => {
      const details = tournamentDetails?.[index];
      return details?.date === selectedDateFilter;
    });
  }, [isGameDetailsPage, tournaments, tournamentDetails, selectedDateFilter]);

  // Filter tournament details and bookings info based on filtered tournaments
  const filteredTournamentDetails = useMemo(() => {
    if (!isGameDetailsPage || !selectedDateFilter || !tournamentDetails) return tournamentDetails;

    return tournamentDetails.filter((details: any) => {
      return details?.date === selectedDateFilter;
    });
  }, [isGameDetailsPage, selectedDateFilter, tournamentDetails]);

  const filteredTournamentBookingsInfo = useMemo(() => {
    if (!isGameDetailsPage || !selectedDateFilter || !tournamentBookingsInfo) return tournamentBookingsInfo;

    return tournamentBookingsInfo.filter((_, index: number) => {
      const details = tournamentDetails?.[index];
      return details?.date === selectedDateFilter;
    });
  }, [isGameDetailsPage, selectedDateFilter, tournamentBookingsInfo, tournamentDetails]);

  // Group tournaments by game name if not on the game details page
  const groupedTournaments = useMemo(() => {
    if (isGameDetailsPage) return null;

    const groups = tournaments?.reduce((acc, tournament) => {
      const gameName = tournament.name;
      if (!acc[gameName]) {
        acc[gameName] = [];
      }
      acc[gameName].push(tournament);
      return acc;
    }, {} as Record<string, typeof tournaments>);

    return groups;
  }, [tournaments, isGameDetailsPage]);

  useEffect(() => {
    if (isGameDetailsPage && tournaments?.length > 0) {
      // Fetch details for all tournaments on the game details page
      fetchAllTournamentsDetails();
    }
  }, [isGameDetailsPage, tournaments]);

  const fetchAllTournamentsDetails = async () => {
    if (!tournaments || tournaments.length === 0) return;
    
    setIsLoading(true);
    try {
      const detailsPromises = tournaments.map(tournament => 
        api.get(API_ENDPOINTS.GET_TOURNAMENT_DETAILS(tournament.tournament_id))
      );
      
      const detailsResponses = await Promise.all(detailsPromises);
      const details = detailsResponses.map(res => res.data);
      setTournamentDetails(details);
      
      const bookingsPromises = tournaments.map(tournament => 
        api.get(API_ENDPOINTS.GET_TOURNAMENT_BOOKINGS_DETAILS(tournament.tournament_id))
          .then(res => res.data?.data || [])
          .catch(() => [])
      );
      
      const bookings = await Promise.all(bookingsPromises);
      setTournamentBookingsInfo(bookings);
    } catch (error) {
      console.error("Error fetching tournament details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshBookingsData = async (tournamentId: string) => {
    try {
      const res = await api.get(
        API_ENDPOINTS.GET_TOURNAMENT_BOOKINGS_DETAILS(tournamentId)
      );

      if (res.status === 200) {
        const freshData = res?.data?.data;
        // Update the bookings info for this specific tournament
        setTournamentBookingsInfo(prev => {
          const index = tournaments.findIndex(t => t.tournament_id === tournamentId);
          if (index === -1) return prev;

          const newBookings = [...prev];
          newBookings[index] = freshData;
          return newBookings;
        });
        return freshData;
      }
      return [];
    } catch {
      return [];
    }
  };

  const refreshSingleTournament = async (tournamentId: string) => {
    try {
      // Fetch updated tournament details (includes updated booking counts in time slots)
      const detailsRes = await api.get(API_ENDPOINTS.GET_TOURNAMENT_DETAILS(tournamentId));

      if (detailsRes.status === 200) {
        const updatedDetails = detailsRes.data;

        // Update the tournament details for this specific tournament
        setTournamentDetails((prev: TournamentDetails[]) => {
          const index = tournaments.findIndex(t => t.tournament_id === tournamentId);
          if (index === -1) return prev;

          const newDetails = [...prev];
          newDetails[index] = updatedDetails;
          return newDetails;
        });
      }

      // Also refresh bookings data
      await refreshBookingsData(tournamentId);
    } catch (error) {
      console.error("Error refreshing tournament:", error);
    }
  };

  const handleJoin = () => {
    // Refresh data after joining to get updated booking counts
    fetchAllTournamentsDetails();
  };

  return (
    <div className="">
      {sectionTitle && (
        <div className="flex items-center gap-2 mb-5">
          <h1 className="text-2xl font-bold text-white">{sectionTitle}</h1>
        </div>
      )}

      {isGameDetailsPage ? (
        // Show hybrid tournament cards on game details page
        <div className="flex flex-col gap-6">
          {(selectedDateFilter ? filteredTournaments : tournaments)?.length > 0 ? (
            (selectedDateFilter ? filteredTournaments : tournaments)?.map((tournament, index) => {
              const detailsArray = selectedDateFilter ? filteredTournamentDetails : tournamentDetails;
              const bookingsArray = selectedDateFilter ? filteredTournamentBookingsInfo : tournamentBookingsInfo;

              return (
                <HybridTournamentCard
                  key={tournament?.tournament_id}
                  tournamentDetails={detailsArray ? detailsArray[index] : null}
                  tournamentBookingsInfo={bookingsArray[index] || null}
                  refreshBookingsData={() => refreshBookingsData(tournament.tournament_id)}
                  onDataRefresh={() => refreshSingleTournament(tournament.tournament_id)}
                  isLoading={isLoading || tournamentDetails.length === 0}
                />
              );
            })
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-400 text-lg">
                {selectedDateFilter
                  ? "No tournaments found for the selected date."
                  : "No tournaments available."}
              </p>
            </div>
          )}
        </div>
      ) : (
        // Show grouped game cards on home page
        <div className="flex items-center gap-x-3 gap-y-6 justify-start flex-wrap">
          {groupedTournaments && Object.entries(groupedTournaments).map(([gameName, gameTournaments]) => (
            <GroupedGameCard
              key={`${sectionTitle}-${gameName}`}
              gameName={gameName}
              gameImage={gameTournaments[0]?.image || ""}
              type={sectionTitle}
              tournamentCount={gameTournaments.length}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default TournamentSection;
