"use client";
import { TournamentConfirmationCardProps } from "@/app/types/CommonComponent.types";
import { formatDate } from "@/app/utils/helper";
import { ArrowRightIcon } from "@heroicons/react/16/solid";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import FullPageLoader from "../common/FullPageLoader";
import { useDispatch, useSelector } from "react-redux";
import { setSelectedTournament } from "@/redux/slices/selectedTournamentSlice";
import BookingSuccess from "../bookingSuccess";
import { toast } from "react-toastify";
import ArrowUturnLeftIcon from "@heroicons/react/24/outline/ArrowUturnLeftIcon";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import api from "@/app/utils/axiosInstance";
import { setUser } from "@/redux/slices/userSlice";
import Link from "next/link";
import Image from "next/image";
import { RootState } from "@/redux/store";

interface CreateBookingParams {
  tournament_id: string;
  slot_id: string;
}

const TournamentConfirmationCard: React.FC<TournamentConfirmationCardProps> = ({
  tournamentDetails,
  selectedTime,
  onBack,
  onJoin,
}) => {
  const [bookingDetails, setBookingDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);

  const router = useRouter();
  const dispatch = useDispatch();
  const accessToken = Cookies.get("access_token");
  const refreshToken = Cookies.get("refresh_token");
  const user = useSelector((state: RootState) => state.user);

  const createDirectBooking = async (params: CreateBookingParams) => {
    try {
      setIsLoading(true);
      const response = await api.post(API_ENDPOINTS.BOOKINGS, params);
      if (response?.status === 200) {
        const userRes = await api.get(API_ENDPOINTS.GET_USER);
        if (userRes.status === 200) {
          dispatch(setUser(userRes?.data?.data));
        }
      }
      return response?.data?.data;
    } catch (error: any) {
      setPaymentError(error?.response?.data?.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const onJoinTournamentClick = async () => {
    setIsLoading(true);
    if (!accessToken && !refreshToken) {
      dispatch(setSelectedTournament({ tournamentDetails, selectedTime }));
      router.push(
        `/login?tournament_id=${tournamentDetails?.tournament_id}&slot_id=${selectedTime?.id}`
      );
      return;
    }
    try {
      const bookingData = await createDirectBooking({
        tournament_id: tournamentDetails?.tournament_id as string,
        slot_id: selectedTime?.id.toString(),
      });
      setBookingDetails(bookingData);
      toast.success("Tournament booked successfully!");
      return;
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {!bookingDetails ? (
        <div className="relative bg-[#141517] rounded-[30px] border border-[#707070] text-white w-[700px] p-6">
          <button
            onClick={onBack}
            className="absolute -top-5 right-0 bg-red-500 text-white rounded-full h-9 w-9 flex items-center justify-center font-semibold cursor-pointer hover:bg-red-600 transition-colors"
          >
            <ArrowUturnLeftIcon className="h-5 w-5 font-semibold" />
          </button>
          {isLoading && <FullPageLoader />}

          {/* Header Section */}
          <div className="mb-6">
{/*             <h2 className="text-white text-5xl font-semibold text-center mb-4">
              {tournamentDetails?.name}
            </h2> */}
            <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e]">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-10 bg-[#c9ff88] rounded-sm"></div>
                  <div>
                    <p className="text-white text-xs uppercase tracking-wider opacity-70">Game</p>
                    <p className="font-bold text-white text-base">
                      {tournamentDetails?.description}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-10 bg-[#c9ff88] rounded-sm"></div>
                  <div>
                    <p className="text-white text-xs uppercase tracking-wider opacity-70">Schedule</p>
                    <p className="font-bold text-white text-base">
                      <span>{selectedTime?.formatted_time}, </span>
                      {formatDate(tournamentDetails?.date)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Confirmation Section */}
          <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e] mb-4">
            <div className="flex items-center mb-3">
              <div className="w-2 h-6 bg-[#c9ff88] rounded-sm mr-2"></div>
              <h3 className="text-white text-xl font-semibold uppercase tracking-wider">Confirmation</h3>
            </div>
            <div className="flex justify-between items-center bg-[#141517] rounded-md p-3 border border-[#2a2c2e]">
              <div className="flex items-center gap-2">
                <p className="text-white text-xs uppercase tracking-wider opacity-100 drop-shadow-[0_0_8px_rgba(255,0,0,0.8)]">Final Amount:</p>
                <span className="font-bold text-white text-2xl drop-shadow-[0_0_8px_rgba(255,0,0,0.8)]">
                  ₹{tournamentDetails?.payment?.final_amount?.toLocaleString("en-IN")}
                </span>
              </div>
            </div>
          </div>
          {/* Payment Breakdown Section
{/*           <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e] mb-4">
            <div className="flex items-center mb-3">
              <div className="w-2 h-6 bg-[#c9ff88] rounded-sm mr-2"></div>
              <h3 className="text-white text-lg font-semibold uppercase tracking-wider">Payment Breakdown</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between bg-[#141517] rounded-md p-2 border border-[#2a2c2e]">
                <div className="flex items-center gap-2">
                  <span className="text-white text-sm">A. Entry Fee</span>
                </div>
                <span className="text-white font-semibold">
                  ₹{tournamentDetails?.payment?.join_price}
                </span>
              </div>
              <div className="flex items-center justify-between bg-[#141517] rounded-md p-2 border border-[#2a2c2e]">
                <div className="flex items-center gap-2">
                  <span className="text-white text-sm">B. Platform Fee</span>
                  <span className="text-white text-xs opacity-70">(2% of A)</span>
                </div>
                <span className="text-white font-semibold">
                  ₹{tournamentDetails?.payment?.platform_fees?.toLocaleString("en-IN")}
                </span>
              </div>
              <div className="flex items-center justify-between bg-[#141517] rounded-md p-2 border border-[#2a2c2e]">
                <div className="flex items-center gap-2">
                  <span className="text-white text-sm">C. GST</span>
                  <span className="text-white text-xs opacity-70">(28% of A+B)</span>
                </div>
                <span className="text-white font-semibold">
                  ₹{tournamentDetails?.payment?.tax_amount?.toLocaleString("en-IN")}
                </span>
              </div>
              <div className="flex items-center justify-between bg-red-600 rounded-md p-3 border border-red-500">
                <div className="flex items-center gap-2">
                  <span className="text-white text-sm font-semibold">D. Total Amount To Pay</span>
                  <span className="text-white text-xs opacity-90">(A+B+C)</span>
                </div>
                <span className="text-white font-bold text-lg">
                  ₹{tournamentDetails?.payment?.final_amount?.toLocaleString("en-IN")}
                </span>
              </div>
            </div>
          </div> */}
          {/* Error Message Section */}
          {paymentError && (
            <div className="bg-[#1a1c1e] rounded-lg p-3 border border-red-500 mb-4">
              <div className="flex items-center">
                <div className="w-2 h-6 bg-red-500 rounded-sm mr-2"></div>
                <span className="text-red-400 text-sm font-medium">{paymentError}</span>
              </div>
            </div>
          )}

          {/* Action Buttons Section */}
          <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e]">
            <div className="flex items-center justify-between gap-4">
              <Link
                href="/my-wallet"
                className="flex items-center group gap-2.5 px-4 py-2 rounded-[10px] bg-[#c9ff88] hover:bg-[#a7f04e] font-semibold text-lg text-[#070b28] transition-colors"
              >
                <Image
                  src="/icons/wallet.png"
                  alt="wallet"
                  width={24}
                  height={24}
                  className="grayscale brightness-0"
                />
                <span>₹{user?.wallet?.toLocaleString("en-IN")}</span>
              </Link>
              <button
                onClick={onJoinTournamentClick}
                className="bg-[#c9ff88] flex items-center justify-center gap-2 text-[#070b28] py-2 px-6 flex-1 rounded-[10px] hover:bg-[#a7f04e] transition-colors font-semibold text-base"
              >
                <span>
                  {accessToken ? "JOIN TOURNAMENT" : "Login to Join Tournament"}
                </span>
                <ArrowRightIcon
                  aria-hidden="true"
                  className="h-5 w-5 shrink-0 font-semibold"
                />
              </button>
            </div>
          </div>
        </div>
      ) : (
        <BookingSuccess data={bookingDetails} onComplete={onJoin} />
      )}
    </>
  );
};

export default TournamentConfirmationCard;
