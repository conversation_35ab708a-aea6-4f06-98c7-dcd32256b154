"use client";
import React, { useState, useEffect } from "react";
import { TimeSlot, TournamentDetails } from "@/app/types/CommonComponent.types";
import PlayerProgressBar from "../PlayerProgressBar";
import { Modal } from "../modal";
import TournamentConfirmationCard from "../tournamentConfirmationCard";
import StartGameModal from "../startGameModal";
import Loader from "../common/Loader";
import { ShareIcon } from "@heroicons/react/24/outline";

interface HybridTournamentCardProps {
  tournamentDetails: TournamentDetails | null;
  tournamentBookingsInfo: any[] | null;
  refreshBookingsData: () => Promise<any[]>;
  onDataRefresh: () => void;
  isLoading?: boolean;
}

const HybridTournamentCard: React.FC<HybridTournamentCardProps> = ({
  tournamentDetails,
  tournamentBookingsInfo,
  refreshBookingsData,
  onDataRefresh,
  isLoading = false,
}) => {
  const [selectedTime, setSelectedTime] = useState<TimeSlot | null>(null);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [showGameDetailsModal, setShowGameDetailsModal] = useState(false);
  const [startGame, setStartGame] = useState<any>(null);
  const [copied, setCopied] = useState(false);

  const handleTimeChange = (slot: TimeSlot) => {
    setSelectedTime(slot);
  };

  const handleJoinNow = () => {
    if (selectedTime) {
      setShowConfirmationModal(true);
    }
  };

  const onStartGameClick = (slotId: number) => {
    const currentGame = tournamentBookingsInfo?.find(
      (booking: any) => booking.slot_id === slotId
    );
    setStartGame(currentGame);
    setShowGameDetailsModal(true);
  };

  // Watch for changes in tournamentBookingsInfo and update startGame if it's currently open
  useEffect(() => {
    if (startGame?.slot_id && tournamentBookingsInfo) {
      const updatedGame = tournamentBookingsInfo.find(
        (booking: any) => booking.slot_id === startGame.slot_id
      );
      if (updatedGame) {
        setStartGame(updatedGame);
      }
    }
  }, [tournamentBookingsInfo, startGame?.slot_id]);

  const handleRefreshGameDetails = async () => {
    if (refreshBookingsData) {
      try {
        const freshBookingsData = await refreshBookingsData();

        if (startGame?.slot_id && freshBookingsData) {
          const updatedGame = freshBookingsData.find(
            (booking: any) => booking.slot_id === startGame.slot_id
          );

          if (updatedGame) {
            setStartGame(updatedGame);
          }
        }
      } catch (error) {
        console.error('Failed to refresh game details:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="relative bg-[#141517] rounded-[30px] border border-[#707070] text-white w-full mb-6 h-[312px] flex justify-center items-center">
        <Loader />
      </div>
    );
  }

  return (
    <>
      <div className="relative bg-[#141517] rounded-[30px] border border-[#707070] text-white w-full mb-6">
        <div className="flex p-6">
          {/* Left section - Creator Image */}
          <div className="flex-shrink-0 w-[160px] flex flex-col justify-center items-center">
            {/* Share Button styled like creator name button but positioned above the image */}
            {tournamentDetails && (
              <div className="w-full mb-2">
                <div className="relative flex justify-center">
                  <button
                    onClick={() => {
                      // Simply copy the current URL from the browser's address bar
                      const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
                      navigator.clipboard.writeText(currentUrl);
                      setCopied(true);
                      setTimeout(() => setCopied(false), 2000);
                    }}
                    className="bg-[#c9ff88] text-[#070b28] px-3 py-1 rounded-[10px] text-sm font-semibold hover:bg-[#a7f04e] transition-colors flex items-center gap-1"
                  >
                    <ShareIcon className="h-4 w-4" />
                    <span>Share</span>
                  </button>
                  {copied && (
                    <div className="absolute -bottom-8 left-0 right-0 mx-auto w-max bg-black bg-opacity-75 text-white text-xs py-1 px-2 rounded whitespace-nowrap z-10">
                      Link copied!
                    </div>
                  )}
                </div>
              </div>
            )}
            <div className="relative mb-2">
              <img
                src={tournamentDetails?.created_by?.image || "/icons/avatar.png"}
                alt="Creator"
                className="w-[140px] h-[140px] rounded-lg object-cover border-2 border-[#c9ff88]"
              />
              <div className="absolute -bottom-2 left-0 right-0 mx-auto w-max bg-[#141517] px-2 py-1 rounded-md border border-[#c9ff88]">
                <span className="text-xs text-[#c9ff88] font-medium">Creator</span>
              </div>
            </div>
            <a
              href={tournamentDetails?.created_by?.youtube_link || "#"}
              target="_blank"
              rel="noopener noreferrer"
              className="flex flex-col items-center w-full bg-[#c9ff88] text-[#070b28] px-3 py-1 rounded-[10px] text-sm font-semibold hover:bg-[#a7f04e] transition-colors mt-2"
            >
              <span className="text-center leading-tight">{tournamentDetails?.created_by?.name}</span>
            </a>
          </div>

          {/* <div className="flex-shrink-0 w-[160px] flex flex-col justify-center items-center">
            <img
              src={tournamentDetails?.image || "/icons/avatar.png"}
              alt="Game Image"
              className="w-[120px] h-[120px] rounded-md object-cover mb-3"
            />
          </div> */}

          {/* Middle section - Game Info */}
          <div className="flex-1 mx-3 flex flex-col space-y-2">
            {/* Tournament details header */}
            <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e]">
              <div className="grid grid-cols-2 gap-4 mb-2">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-10 bg-[#c9ff88] rounded-sm"></div>
                  <div>
                    <p className="text-white text-xs uppercase tracking-wider opacity-70">Map</p>
                    <p className="font-bold text-white text-base">
                      {tournamentDetails?.map}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-10 bg-[#c9ff88] rounded-sm"></div>
                  <div>
                    <p className="text-white text-xs uppercase tracking-wider opacity-70">Mode</p>
                    <p className="font-bold text-white text-base">
                      {tournamentDetails?.mode}
                    </p>
                  </div>
                </div>
              </div>
              
              {/* Prize and fee info */}
              <div className="flex justify-between items-center bg-[#141517] rounded-md p-2 border border-[#2a2c2e]">
                {tournamentDetails?.per_kill_prize ? (
                  <div className="flex items-center gap-2">
                    <p className="text-white text-xs uppercase tracking-wider opacity-70">Prize Pool:</p>
                    <span className="font-bold text-white text-2xl drop-shadow-[0_0_8px_rgba(255,0,0,0.8)]">
                      ₹{tournamentDetails?.prize_pool.toLocaleString("en-IN")}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <p className="text-white text-xs uppercase tracking-wider opacity-100 drop-shadow-[0_0_8px_rgba(255,0,0,0.8)]">Prize Pool:</p>
                    <span className="font-bold text-white text-2xl drop-shadow-[0_0_8px_rgba(255,0,0,0.8)]">
                      ₹{tournamentDetails?.prize_pool?.toLocaleString("en-IN")}
                    </span>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <p className="text-white text-xs uppercase tracking-wider opacity-100 drop-shadow-[0_0_8px_rgba(255,0,0,0.8)]">Join Fee:</p>
                  <span className="font-bold text-white text-2xl drop-shadow-[0_0_8px_rgba(255,0,0,0.8)]">
                    ₹{tournamentDetails?.payment?.final_amount?.toLocaleString("en-IN")}
                  </span>
                </div>
              </div>
            </div>

            {/* Prize breakdown */}
            <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e]">
              <div className="flex items-center mb-2">
                <div className="w-2 h-5 bg-[#c9ff88] rounded-sm mr-2"></div>
                <p className="text-white text-xs uppercase tracking-wider opacity-100">Tournament Date: <span className="font-semibold normal-case opacity-100">{tournamentDetails?.date}</span></p>
              </div>
              
              {tournamentDetails?.per_kill_prize ? (
                <div className="flex items-center justify-between px-2 py-4 bg-[#141517] rounded-md border border-[#2a2c2e] h-[72px]">
                  <p className="text-white text-xs uppercase tracking-wider opacity-70">Per Kill Prize</p>
                  <p className="font-bold text-white text-base ">
                    ₹{tournamentDetails?.per_kill_prize.toLocaleString("en-IN")}
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-3 gap-2">
                  <div className="flex flex-col items-center justify-center px-2 py-1 bg-[#141517] rounded-md border border-[#2a2c2e]">
                    <p className="text-white text-xs uppercase tracking-wider opacity-70">1st Prize</p>
                    <p className="font-bold text-white text-base ">
                      ₹{tournamentDetails?.first_prize?.toLocaleString("en-IN")}
                    </p>
                  </div>
                  <div className="flex flex-col items-center justify-center px-2 py-1 bg-[#141517] rounded-md border border-[#2a2c2e]">
                    <p className="text-white text-xs uppercase tracking-wider opacity-70">2nd Prize</p>
                    <p className="font-bold text-white text-base ">
                      ₹{tournamentDetails?.second_prize?.toLocaleString("en-IN")}
                    </p>
                  </div>
                  <div className="flex flex-col items-center justify-center px-2 py-1 bg-[#141517] rounded-md border border-[#2a2c2e]">
                    <p className="text-white text-xs uppercase tracking-wider opacity-70">3rd Prize</p>
                    <p className="font-bold text-white text-base">
                      ₹{tournamentDetails?.third_prize?.toLocaleString("en-IN")}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right section - Time Slots */}
          <div className="w-[700px] flex-shrink-0">
            <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e] mb-2">
              <div className="max-h-[300px] overflow-y-auto pr-2 scrollbar-hide max-w-[750px]">
                <div className="grid grid-cols-3 gap-2">
                  {tournamentDetails?.time_slots.slice(0, 6).map((slot, index) => (
                    <div
                      key={index}
                      className="flex flex-col mb-3 pb-3 border border-[#2a2c2e] rounded-md p-3 bg-[#141517]"
                    >
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center">
                        <input
                          type="radio"
                          id={`slot-${tournamentDetails?.tournament_id}-${index}`}
                          name={`timeSlot-${tournamentDetails?.tournament_id}`}
                          value={slot.formatted_time}
                          checked={selectedTime?.formatted_time === slot?.formatted_time}
                          onChange={() => handleTimeChange(slot)}
                          className="mr-2 h-4 w-4 appearance-none border-2 border-[#2a2c2e] bg-[#141517] checked:border-[#c9ff88] checked:bg-[#141517] relative before:content-[''] before:absolute before:top-1/2 before:left-1/2 before:transform before:-translate-x-1/2 before:-translate-y-1/2 before:w-2 before:h-2 before:bg-[#c9ff88] before:opacity-0 checked:before:opacity-100 cursor-pointer"
                          disabled={slot.is_expired || slot.is_cancelled}
                        />
                        <label
                          htmlFor={`slot-${tournamentDetails?.tournament_id}-${index}`}
                          className={`text-white text-sm cursor-pointer ${slot.is_expired || slot.is_cancelled ? "line-through opacity-50" : ""}`}
                        >
                          {slot?.formatted_time}
                          {/* {formatDate(tournamentDetails?.date)} */}
                        </label>
                      </div>
                      <div>
                        <button
                          onClick={() => onStartGameClick(slot.id)}
                          className={`rounded-md bg-red-600 px-2 py-1 text-sm font-semibold text-white shadow-sm whitespace-nowrap min-w-[90px] text-center ${slot.is_cancelled || !tournamentBookingsInfo?.find((booking: any) => booking.slot_id === slot.id)?.booking_id ? "opacity-50 cursor-not-allowed" : "hover:bg-red-500"}`}
                          disabled={slot.is_cancelled || !tournamentBookingsInfo?.find((booking: any) => booking.slot_id === slot.id)?.booking_id}
                        >
                          {slot.is_cancelled ? "Cancelled" : "Start Game"}
                        </button>
                      </div>
                    </div>
                    <div className={`${slot.is_expired || slot.is_cancelled ? "opacity-50" : ""}`}>
                      <PlayerProgressBar
                        bookingsCount={slot?.bookings_count ?? 0}
                        maxPlayers={slot?.max_players ?? 0}
                        small
                      />
                    </div>
                  </div>
                ))}
                </div>
              </div>
            </div>
            <button
              onClick={handleJoinNow}
              disabled={!selectedTime}
              className={`mt-4 bg-[#c9ff88] text-[#070b28] px-6 py-2 rounded-[10px] text-base font-semibold w-full ${!selectedTime ? "opacity-50 cursor-not-allowed" : ""}`}
            >
              JOIN NOW
            </button>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      <Modal
        modalOpen={showConfirmationModal}
        handleModalOpen={() => setShowConfirmationModal(false)}
      >
        <TournamentConfirmationCard
          tournamentDetails={tournamentDetails}
          selectedTime={selectedTime!}
          onJoin={() => {
            setShowConfirmationModal(false);
            onDataRefresh();
          }}
          onBack={() => setShowConfirmationModal(false)}
        />
      </Modal>

      {/* Start Game Modal */}
      <Modal
        modalOpen={showGameDetailsModal}
        handleModalOpen={() => setShowGameDetailsModal(false)}
        showRefreshButton={true}
        onRefresh={handleRefreshGameDetails}
      >
        <StartGameModal
          game_link={(startGame?.game_link as string) || ""}
          room_id={(startGame?.room_id as string) || ""}
          room_password={(startGame?.room_password as string) || ""}
          bookingId={startGame?.booking_id}
          tournamentName={tournamentDetails?.name || ""}
          inGameName={startGame?.in_game_name || ""}
          showUpdateInGameNameForm={true}
          createdBy={tournamentDetails?.created_by}
        />
      </Modal>
    </>
  );
};

export default HybridTournamentCard;