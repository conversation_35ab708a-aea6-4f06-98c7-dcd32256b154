"use client";
import React, { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  BookingSuccessProps,
  UpdateInGameNameResponse,
} from "@/app/types/CommonComponent.types";
import { formatDate } from "@/app/utils/helper";
import UpdateInGameNameForm from "../updateInGameNameForm";

const BookingSuccess: React.FC<BookingSuccessProps> = ({ data, onComplete }) => {
  const [inGameNameRes, setInGameNameRes] =
    useState<UpdateInGameNameResponse | null>(null);

  const router = useRouter();
  const goToMyTournaments = () => {
    if (onComplete) {
      onComplete();
    }
    router.push(`/my-tournaments/${data?.booking_id}`);
  };

  const handleFormSuccess = (response: UpdateInGameNameResponse) => {
    setInGameNameRes(response);

    if (onComplete) {
      onComplete();
    }
  };

  return (
    <div className="relative bg-[#141517] rounded-[30px] border border-[#707070] text-white w-[700px] p-6">
      {/* Success Header Section */}
      <div className="bg-[#1a1c1e] rounded-lg p-4 border border-[#2a2c2e] mb-6">
        <div className="flex items-center justify-center gap-4 mb-3">
          <Image
            src="/icons/success.svg"
            alt="Order success"
            height={48}
            width={48}
            className="h-12 w-12"
          />
          <h2 className="text-3xl font-bold text-white">
            Booking Successful
          </h2>
        </div>
        <div className="text-center">
          <p className="text-white text-sm opacity-90">
            Thank you, your payment has been successful and your tournament
            booking is now confirmed.
          </p>
        </div>
      </div>
      {/* Tournament Details Section */}
      <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e] mb-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-10 bg-[#c9ff88] rounded-sm"></div>
            <div>
              <p className="text-white text-xs uppercase tracking-wider opacity-70">Tournament Name</p>
              <p className="font-bold text-white text-base">
                {data?.tournament?.name}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-10 bg-[#c9ff88] rounded-sm"></div>
            <div>
              <p className="text-white text-xs uppercase tracking-wider opacity-70">Slot</p>
              <p className="font-bold text-white text-base">
                <span>{data?.time_slot?.formatted_time}, </span>
                {formatDate(data?.tournament?.date)}
              </p>
            </div>
          </div>
        </div>
      </div>
      {/* In Game Name Section */}
      {inGameNameRes && (
        <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e] mb-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
            <div className="flex items-center justify-between flex-1">
              <span className="text-white text-xs uppercase tracking-wider opacity-70">Your In Game Name:</span>
              <span className="font-bold text-white text-base">
                {inGameNameRes?.in_game_name}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Update In Game Name Form Section */}
      {!inGameNameRes && (
        <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e] mb-4">
          <UpdateInGameNameForm
            bookingId={data?.booking_id}
            onSuccess={handleFormSuccess}
            tournamentName={data?.tournament?.name}
          />
        </div>
      )}

      {/* Action Button Section */}
      <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e]">
        <div className="flex justify-center">
          <button
            onClick={goToMyTournaments}
            disabled={!inGameNameRes}
            className={`bg-[#c9ff88] text-[#070b28] px-6 py-2 rounded-[10px] text-base font-semibold transition-colors ${
              !inGameNameRes
                ? "opacity-50 cursor-not-allowed"
                : "hover:bg-[#a7f04e]"
            }`}
          >
            View Booking Details
          </button>
        </div>
      </div>
    </div>
  );
};

export default BookingSuccess;
